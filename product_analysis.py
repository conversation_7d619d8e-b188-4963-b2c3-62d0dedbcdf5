import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib
matplotlib.use('Agg')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def parse_number(value_str):
    """解析数字，处理万为单位的数据"""
    if value_str == '-' or value_str == '':
        return 0
    
    if '万' in value_str:
        number = float(value_str.replace('万', ''))
        return int(number * 10000)
    
    try:
        return int(value_str)
    except:
        return 0

def parse_date(date_str):
    """将YYYYMM格式转换为datetime对象"""
    year = int(date_str[:4])
    month = int(date_str[4:])
    return datetime(year, month, 1)

def read_data(filename):
    """读取数据文件并解析"""
    data = {}
    current_model = None
    
    with open(filename, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
            
        if '\t' not in line and line != '上线日期(month)\t设备三级分类，产品系列\t上线设备数':
            current_model = line
            data[current_model] = {'dates': [], 'values': []}
            i += 1
            continue
        
        if '上线日期(month)' in line:
            i += 1
            continue
        
        if current_model and '\t' in line:
            parts = line.split('\t')
            if len(parts) >= 3:
                date_str = parts[0].strip()
                value_str = parts[2].strip()
                
                if date_str and len(date_str) == 6:
                    date_obj = parse_date(date_str)
                    value = parse_number(value_str)
                    
                    data[current_model]['dates'].append(date_obj)
                    data[current_model]['values'].append(value)
        
        i += 1
    
    return data

def analyze_product_lifecycle(data):
    """分析产品生命周期"""
    analysis = {}
    
    for model, model_data in data.items():
        if not model_data['dates']:
            continue
            
        dates = model_data['dates']
        values = model_data['values']
        
        # 基本统计
        max_value = max(values)
        max_date = dates[values.index(max_value)]
        first_date = dates[0]
        last_date = dates[-1]
        
        # 计算生命周期阶段
        # 找到首次超过1000台的时间（成长期开始）
        growth_start = None
        for i, value in enumerate(values):
            if value > 1000:
                growth_start = dates[i]
                break
        
        # 找到峰值后连续3个月下降的开始时间（衰退期开始）
        decline_start = None
        peak_index = values.index(max_value)
        if peak_index < len(values) - 3:
            for i in range(peak_index + 1, len(values) - 2):
                if (values[i] > values[i+1] > values[i+2]):
                    decline_start = dates[i]
                    break
        
        # 计算月均增长率（峰值前）
        if peak_index > 0:
            months = peak_index
            growth_rate = ((max_value / max(values[0], 1)) ** (1/months) - 1) * 100 if months > 0 else 0
        else:
            growth_rate = 0
        
        # 当前状态分析
        recent_values = values[-6:] if len(values) >= 6 else values
        current_trend = "稳定"
        if len(recent_values) >= 3:
            if all(recent_values[i] < recent_values[i-1] for i in range(1, 3)):
                current_trend = "下降"
            elif all(recent_values[i] > recent_values[i-1] for i in range(1, 3)):
                current_trend = "上升"
        
        analysis[model] = {
            'first_date': first_date,
            'last_date': last_date,
            'max_value': max_value,
            'max_date': max_date,
            'growth_start': growth_start,
            'decline_start': decline_start,
            'growth_rate': growth_rate,
            'current_trend': current_trend,
            'current_value': values[-1],
            'lifecycle_months': len(dates)
        }
    
    return analysis

def generate_insights(analysis):
    """生成产品洞察"""
    print("=" * 80)
    print("🎯 产品经理视角：数据洞察与战略建议")
    print("=" * 80)
    
    # 1. 产品组合分析
    print("\n📊 1. 产品组合分析")
    print("-" * 50)
    
    # 按峰值分类
    high_performers = []
    medium_performers = []
    low_performers = []
    
    for model, data in analysis.items():
        if data['max_value'] > 50000:
            high_performers.append((model, data))
        elif data['max_value'] > 10000:
            medium_performers.append((model, data))
        else:
            low_performers.append((model, data))
    
    print(f"🌟 明星产品 (峰值>5万台): {len(high_performers)}款")
    for model, data in high_performers:
        print(f"   • {model}: 峰值{data['max_value']:,}台 ({data['max_date'].strftime('%Y-%m')})")
    
    print(f"\n💼 主力产品 (峰值1-5万台): {len(medium_performers)}款")
    for model, data in medium_performers:
        print(f"   • {model}: 峰值{data['max_value']:,}台 ({data['max_date'].strftime('%Y-%m')})")
    
    print(f"\n🔍 小众产品 (峰值<1万台): {len(low_performers)}款")
    for model, data in low_performers:
        print(f"   • {model}: 峰值{data['max_value']:,}台 ({data['max_date'].strftime('%Y-%m')})")
    
    # 2. 产品生命周期分析
    print("\n⏰ 2. 产品生命周期分析")
    print("-" * 50)
    
    for model, data in analysis.items():
        lifecycle_years = data['lifecycle_months'] / 12
        print(f"\n📱 {model}:")
        print(f"   生命周期: {lifecycle_years:.1f}年 ({data['first_date'].strftime('%Y-%m')} - {data['last_date'].strftime('%Y-%m')})")
        print(f"   当前状态: {data['current_trend']} (当前{data['current_value']:,}台)")
        
        if data['growth_start']:
            growth_months = (data['max_date'] - data['growth_start']).days // 30
            print(f"   成长期: {growth_months}个月 ({data['growth_start'].strftime('%Y-%m')} - {data['max_date'].strftime('%Y-%m')})")
        
        if data['decline_start']:
            decline_months = (data['last_date'] - data['decline_start']).days // 30
            print(f"   衰退期: {decline_months}个月 (从{data['decline_start'].strftime('%Y-%m')}开始)")
    
    # 3. 市场趋势分析
    print("\n📈 3. 市场趋势分析")
    print("-" * 50)
    
    # 按发布时间分组
    early_products = []  # 2020年前
    mid_products = []    # 2020-2022
    recent_products = [] # 2023年后
    
    for model, data in analysis.items():
        year = data['first_date'].year
        if year < 2020:
            early_products.append((model, data))
        elif year <= 2022:
            mid_products.append((model, data))
        else:
            recent_products.append((model, data))
    
    print("🕐 早期产品 (2020年前发布):")
    for model, data in early_products:
        print(f"   • {model}: 发布{data['first_date'].strftime('%Y-%m')}, 峰值{data['max_value']:,}台")
    
    print("\n🕑 中期产品 (2020-2022年发布):")
    for model, data in mid_products:
        print(f"   • {model}: 发布{data['first_date'].strftime('%Y-%m')}, 峰值{data['max_value']:,}台")
    
    print("\n🕒 新产品 (2023年后发布):")
    for model, data in recent_products:
        print(f"   • {model}: 发布{data['first_date'].strftime('%Y-%m')}, 峰值{data['max_value']:,}台")
    
    # 4. 战略建议
    print("\n🎯 4. 战略建议")
    print("-" * 50)
    
    print("💡 产品策略建议:")
    
    # 找出当前上升趋势的产品
    rising_products = [model for model, data in analysis.items() if data['current_trend'] == '上升']
    stable_products = [model for model, data in analysis.items() if data['current_trend'] == '稳定']
    declining_products = [model for model, data in analysis.items() if data['current_trend'] == '下降']
    
    if rising_products:
        print(f"\n🚀 重点投入 (上升趋势): {', '.join(rising_products)}")
        print("   建议: 加大营销投入，扩大产能，抢占市场份额")
    
    if stable_products:
        print(f"\n💰 现金牛 (稳定期): {', '.join(stable_products)}")
        print("   建议: 维持现状，优化成本，准备下一代产品")
    
    if declining_products:
        print(f"\n⚠️  需要关注 (下降趋势): {', '.join(declining_products)}")
        print("   建议: 分析原因，考虑产品升级或逐步退市")
    
    # 5. 产品规划建议
    print("\n📋 5. 产品规划建议")
    print("-" * 50)
    
    # 分析产品代际更替
    print("🔄 产品代际分析:")
    models_by_series = {}
    for model in analysis.keys():
        base_name = model.split()[0]  # 取第一部分作为系列名
        if base_name not in models_by_series:
            models_by_series[base_name] = []
        models_by_series[base_name].append(model)
    
    for series, models in models_by_series.items():
        if len(models) > 1:
            print(f"\n📱 {series}系列:")
            for model in sorted(models):
                data = analysis[model]
                print(f"   • {model}: {data['first_date'].strftime('%Y-%m')}发布, 峰值{data['max_value']:,}台, 当前{data['current_trend']}")

if __name__ == "__main__":
    # 读取数据
    data = read_data('data.txt')
    
    # 分析产品生命周期
    analysis = analyze_product_lifecycle(data)
    
    # 生成洞察
    generate_insights(analysis)
