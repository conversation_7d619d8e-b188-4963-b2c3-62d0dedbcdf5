import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime
import re
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def parse_number(value_str):
    """解析数字，处理万为单位的数据"""
    if value_str == '-' or value_str == '':
        return 0
    
    # 处理万为单位的数据
    if '万' in value_str:
        number = float(value_str.replace('万', ''))
        return int(number * 10000)
    
    # 处理普通数字
    try:
        return int(value_str)
    except:
        return 0

def parse_date(date_str):
    """将YYYYMM格式转换为datetime对象"""
    year = int(date_str[:4])
    month = int(date_str[4:])
    return datetime(year, month, 1)

def read_data(filename):
    """读取数据文件并解析"""
    data = {}
    current_model = None
    
    with open(filename, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # 跳过空行
        if not line:
            i += 1
            continue
            
        # 检查是否是型号名称行（不包含制表符的行）
        if '\t' not in line and line != '上线日期(month)\t设备三级分类，产品系列\t上线设备数':
            current_model = line
            data[current_model] = {'dates': [], 'values': []}
            i += 1
            continue
        
        # 跳过表头行
        if '上线日期(month)' in line:
            i += 1
            continue
        
        # 解析数据行
        if current_model and '\t' in line:
            parts = line.split('\t')
            if len(parts) >= 3:
                date_str = parts[0].strip()
                value_str = parts[2].strip()
                
                if date_str and len(date_str) == 6:  # YYYYMM格式
                    date_obj = parse_date(date_str)
                    value = parse_number(value_str)
                    
                    data[current_model]['dates'].append(date_obj)
                    data[current_model]['values'].append(value)
        
        i += 1
    
    return data

def find_first_point_above_threshold(dates, values, threshold=200):
    """找到第一个超过阈值的点"""
    for i, value in enumerate(values):
        if value > threshold:
            return dates[i], value
    return None, None

def create_visualization(data):
    """创建可视化图表"""
    plt.figure(figsize=(16, 10))
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
              '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    
    for i, (model, model_data) in enumerate(data.items()):
        if not model_data['dates']:  # 跳过空数据
            continue
            
        dates = model_data['dates']
        values = model_data['values']
        
        # 绘制主线
        color = colors[i % len(colors)]
        plt.plot(dates, values, marker='o', linewidth=2, markersize=4, 
                label=model, color=color)
        
        # 找到第一个超过200台的点
        first_date, first_value = find_first_point_above_threshold(dates, values, 200)
        
        if first_date and first_value:
            # 绘制垂直虚线标记
            plt.axvline(x=first_date, color=color, linestyle='--', alpha=0.7, linewidth=1)
            
            # 在虚线顶部添加标注
            plt.annotate(f'{model}\n首次>200台\n{first_date.strftime("%Y-%m")}\n{first_value:,}台', 
                        xy=(first_date, first_value), 
                        xytext=(10, 20), 
                        textcoords='offset points',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor=color, alpha=0.3),
                        fontsize=8,
                        ha='left')
    
    # 设置图表属性
    plt.title('产品上线设备数量走势分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('时间', fontsize=12)
    plt.ylabel('产品数量（台）', fontsize=12)
    
    # 设置网格
    plt.grid(True, alpha=0.3)
    
    # 设置图例
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    
    # 自动调整Y轴范围
    plt.ticklabel_format(style='plain', axis='y')
    
    # 旋转x轴标签以避免重叠
    plt.xticks(rotation=45)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('产品走势分析.png', dpi=300, bbox_inches='tight')
    plt.savefig('产品走势分析.pdf', bbox_inches='tight')

    # 关闭图表以释放内存
    plt.close()

def print_summary(data):
    """打印数据摘要"""
    print("数据摘要:")
    print("-" * 50)
    
    for model, model_data in data.items():
        if not model_data['dates']:
            continue
            
        dates = model_data['dates']
        values = model_data['values']
        
        max_value = max(values)
        max_date = dates[values.index(max_value)]
        
        first_date, first_value = find_first_point_above_threshold(dates, values, 200)
        
        print(f"型号: {model}")
        print(f"  数据点数: {len(dates)}")
        print(f"  最大值: {max_value:,}台 ({max_date.strftime('%Y-%m')})")
        
        if first_date:
            print(f"  首次超过200台: {first_date.strftime('%Y-%m')} ({first_value:,}台)")
        else:
            print(f"  未超过200台")
        print()

if __name__ == "__main__":
    # 读取数据
    data = read_data('data.txt')
    
    # 打印摘要
    print_summary(data)
    
    # 创建可视化
    create_visualization(data)
    
    print("可视化图表已生成并保存为 '产品走势分析.png' 和 '产品走势分析.pdf'")
