import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
from scipy import stats
from sklearn.linear_model import LinearRegression

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def parse_number(value_str):
    """解析数字，处理万为单位的数据"""
    if value_str == '-' or value_str == '':
        return 0
    
    if '万' in value_str:
        number = float(value_str.replace('万', ''))
        return int(number * 10000)
    
    try:
        return int(value_str)
    except:
        return 0

def parse_date(date_str):
    """将YYYYMM格式转换为datetime对象"""
    year = int(date_str[:4])
    month = int(date_str[4:])
    return datetime(year, month, 1)

def read_data(filename):
    """读取数据文件并解析"""
    data = {}
    current_model = None
    
    with open(filename, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
            
        if '\t' not in line and line != '上线日期(month)\t设备三级分类，产品系列\t上线设备数':
            current_model = line
            data[current_model] = {'dates': [], 'values': []}
            i += 1
            continue
        
        if '上线日期(month)' in line:
            i += 1
            continue
        
        if current_model and '\t' in line:
            parts = line.split('\t')
            if len(parts) >= 3:
                date_str = parts[0].strip()
                value_str = parts[2].strip()
                
                if date_str and len(date_str) == 6:
                    date_obj = parse_date(date_str)
                    value = parse_number(value_str)
                    
                    data[current_model]['dates'].append(date_obj)
                    data[current_model]['values'].append(value)
        
        i += 1
    
    return data

def merge_data_by_wifi_standard(data):
    """按WiFi标准合并数据"""
    wifi_groups = {
        'WiFi 5': ['EW1200', 'EW1200G PRO', 'EW1300G'],
        'WiFi 6': ['EW1800GX', 'EW1800GX PRO', 'EW3000GX', 'EW3000GX PRO', 'EW3200GX', 'EW6000GX PRO'],
        'WiFi 7': ['EW5100BE PRO']
    }
    
    merged_data = {}
    for wifi_std, models in wifi_groups.items():
        merged_data[wifi_std] = {'dates': [], 'values': [], 'models': models}
    
    # 获取所有日期
    all_dates = set()
    for model, model_data in data.items():
        for date in model_data['dates']:
            all_dates.add(date)
    
    all_dates = sorted(list(all_dates))
    
    # 为每个WiFi标准在每个日期计算总值
    for wifi_std, wifi_data in merged_data.items():
        for date in all_dates:
            total_value = 0
            for model in wifi_data['models']:
                if model in data:
                    model_data = data[model]
                    if date in model_data['dates']:
                        idx = model_data['dates'].index(date)
                        total_value += model_data['values'][idx]
            
            wifi_data['dates'].append(date)
            wifi_data['values'].append(total_value)
    
    return merged_data, all_dates

def analyze_wifi5_wifi6_relationship(merged_data):
    """分析WiFi 5和WiFi 6的关系"""
    print("📊 WiFi 5与WiFi 6关系分析")
    print("-" * 50)
    
    wifi5_data = merged_data['WiFi 5']
    wifi6_data = merged_data['WiFi 6']
    
    # 找到WiFi 6开始显著上升的时间点
    wifi6_values = wifi6_data['values']
    wifi6_dates = wifi6_data['dates']
    
    # 找到WiFi 6连续3个月增长超过1000台的起始点
    wifi6_surge_start = None
    for i in range(len(wifi6_values) - 2):
        if (wifi6_values[i+1] - wifi6_values[i] > 1000 and 
            wifi6_values[i+2] - wifi6_values[i+1] > 1000):
            wifi6_surge_start = wifi6_dates[i]
            break
    
    if wifi6_surge_start:
        print(f"🚀 WiFi 6显著上升开始时间: {wifi6_surge_start.strftime('%Y-%m')}")
        
        # 分析同期WiFi 5的变化
        surge_index = wifi6_dates.index(wifi6_surge_start)
        if surge_index < len(wifi5_data['values']):
            wifi5_before = wifi5_data['values'][max(0, surge_index-3):surge_index]
            wifi5_after = wifi5_data['values'][surge_index:min(len(wifi5_data['values']), surge_index+6)]
            
            avg_before = sum(wifi5_before) / len(wifi5_before) if wifi5_before else 0
            avg_after = sum(wifi5_after) / len(wifi5_after) if wifi5_after else 0
            
            print(f"📉 WiFi 5在WiFi 6上升期间的变化:")
            print(f"   上升前平均: {avg_before:,.0f}台")
            print(f"   上升后平均: {avg_after:,.0f}台")
            print(f"   变化幅度: {((avg_after - avg_before) / avg_before * 100):+.1f}%")
    
    return wifi6_surge_start

def predict_wifi7_surge(merged_data, wifi6_surge_start):
    """预测WiFi 7的上升时间点"""
    print("\n🔮 WiFi 7上升时间预测")
    print("-" * 50)
    
    wifi7_data = merged_data['WiFi 7']
    wifi7_values = wifi7_data['values']
    wifi7_dates = wifi7_data['dates']
    
    # 找到WiFi 7开始有数据的时间点
    wifi7_start = None
    for i, value in enumerate(wifi7_values):
        if value > 0:
            wifi7_start = wifi7_dates[i]
            break
    
    if wifi7_start and wifi6_surge_start:
        # 计算WiFi 6从开始到显著上升的时间间隔
        wifi6_data = merged_data['WiFi 6']
        wifi6_first_date = None
        for i, value in enumerate(wifi6_data['values']):
            if value > 0:
                wifi6_first_date = wifi6_data['dates'][i]
                break
        
        if wifi6_first_date:
            wifi6_incubation_months = (wifi6_surge_start.year - wifi6_first_date.year) * 12 + \
                                    (wifi6_surge_start.month - wifi6_first_date.month)
            
            print(f"📈 WiFi 6模式分析:")
            print(f"   首次出现: {wifi6_first_date.strftime('%Y-%m')}")
            print(f"   显著上升: {wifi6_surge_start.strftime('%Y-%m')}")
            print(f"   孵化期: {wifi6_incubation_months}个月")
            
            # 预测WiFi 7的显著上升时间
            predicted_wifi7_surge = datetime(wifi7_start.year + (wifi7_start.month + wifi6_incubation_months - 1) // 12,
                                           (wifi7_start.month + wifi6_incubation_months - 1) % 12 + 1, 1)
            
            print(f"\n🎯 WiFi 7预测:")
            print(f"   首次出现: {wifi7_start.strftime('%Y-%m')}")
            print(f"   预测显著上升: {predicted_wifi7_surge.strftime('%Y-%m')}")
            print(f"   预计孵化期: {wifi6_incubation_months}个月")
            
            # 分析当前WiFi 7的增长趋势
            recent_wifi7 = wifi7_values[-6:] if len(wifi7_values) >= 6 else wifi7_values
            if len(recent_wifi7) >= 2:
                growth_rates = []
                for i in range(1, len(recent_wifi7)):
                    if recent_wifi7[i-1] > 0:
                        growth_rate = (recent_wifi7[i] - recent_wifi7[i-1]) / recent_wifi7[i-1] * 100
                        growth_rates.append(growth_rate)
                
                if growth_rates:
                    avg_growth = sum(growth_rates) / len(growth_rates)
                    print(f"   当前月均增长率: {avg_growth:.1f}%")
                    
                    if avg_growth > 20:
                        print(f"   🚨 WiFi 7可能已经进入快速上升期！")
                    elif avg_growth > 10:
                        print(f"   ⚡ WiFi 7正在加速，接近上升期")
                    else:
                        print(f"   🐌 WiFi 7仍在孵化期")
            
            return predicted_wifi7_surge
    
    return None

def create_total_trend_analysis(merged_data, all_dates):
    """创建总量趋势分析"""
    print("\n📈 总量趋势分析")
    print("-" * 50)
    
    # 计算总量
    total_values = []
    for date in all_dates:
        total = 0
        for wifi_std, wifi_data in merged_data.items():
            if date in wifi_data['dates']:
                idx = wifi_data['dates'].index(date)
                total += wifi_data['values'][idx]
        total_values.append(total)
    
    # 分析总量趋势
    max_total = max(total_values)
    max_date = all_dates[total_values.index(max_total)]
    current_total = total_values[-1]
    
    print(f"📊 总量统计:")
    print(f"   历史峰值: {max_total:,}台 ({max_date.strftime('%Y-%m')})")
    print(f"   当前总量: {current_total:,}台 ({all_dates[-1].strftime('%Y-%m')})")
    print(f"   峰值变化: {((current_total - max_total) / max_total * 100):+.1f}%")
    
    # 分析最近趋势
    recent_values = total_values[-12:] if len(total_values) >= 12 else total_values
    if len(recent_values) >= 6:
        first_half = recent_values[:len(recent_values)//2]
        second_half = recent_values[len(recent_values)//2:]
        
        avg_first = sum(first_half) / len(first_half)
        avg_second = sum(second_half) / len(second_half)
        
        trend_change = (avg_second - avg_first) / avg_first * 100
        
        print(f"   最近趋势: {trend_change:+.1f}%")
        
        if trend_change > 5:
            print(f"   📈 大盘呈上升趋势")
        elif trend_change < -5:
            print(f"   📉 大盘呈下降趋势")
        else:
            print(f"   📊 大盘相对稳定")
    
    return total_values

def create_comprehensive_visualization(merged_data, all_dates, total_values, predicted_wifi7_surge):
    """创建综合可视化图表"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    colors = {
        'WiFi 5': '#1f77b4',  # 蓝色
        'WiFi 6': '#ff7f0e',  # 橙色
        'WiFi 7': '#2ca02c',  # 绿色
        'Total': '#d62728'    # 红色
    }
    
    # 上图：WiFi标准分别显示
    for wifi_std, wifi_data in merged_data.items():
        dates = wifi_data['dates']
        values = wifi_data['values']
        
        ax1.plot(dates, values, marker='o', linewidth=2, markersize=3, 
                label=wifi_std, color=colors[wifi_std])
    
    # 标记预测的WiFi 7上升时间点
    if predicted_wifi7_surge:
        ax1.axvline(x=predicted_wifi7_surge, color=colors['WiFi 7'], 
                   linestyle=':', alpha=0.8, linewidth=2)
        ax1.annotate(f'预测WiFi 7上升期\n{predicted_wifi7_surge.strftime("%Y-%m")}', 
                    xy=(predicted_wifi7_surge, 0), 
                    xytext=(10, 50), 
                    textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor=colors['WiFi 7'], alpha=0.3),
                    fontsize=9,
                    ha='left')
    
    ax1.set_title('WiFi标准产品走势对比分析', fontsize=14, fontweight='bold')
    ax1.set_ylabel('产品数量（台）', fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=10)
    ax1.tick_params(axis='x', rotation=45)
    
    # 下图：总量趋势
    ax2.plot(all_dates, total_values, marker='o', linewidth=3, markersize=4, 
             color=colors['Total'], label='总量')
    
    # 添加趋势线
    x_numeric = [(date - all_dates[0]).days for date in all_dates]
    z = np.polyfit(x_numeric, total_values, 2)  # 二次多项式拟合
    p = np.poly1d(z)
    ax2.plot(all_dates, p(x_numeric), "--", alpha=0.8, color='gray', label='趋势线')
    
    # 标记峰值
    max_total = max(total_values)
    max_date = all_dates[total_values.index(max_total)]
    ax2.annotate(f'历史峰值\n{max_total:,}台\n{max_date.strftime("%Y-%m")}', 
                xy=(max_date, max_total), 
                xytext=(10, 20), 
                textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.3', facecolor=colors['Total'], alpha=0.3),
                fontsize=9,
                ha='left',
                arrowprops=dict(arrowstyle='->', color=colors['Total']))
    
    ax2.set_title('总体市场规模趋势', fontsize=14, fontweight='bold')
    ax2.set_xlabel('时间', fontsize=11)
    ax2.set_ylabel('总产品数量（台）', fontsize=11)
    ax2.grid(True, alpha=0.3)
    ax2.legend(fontsize=10)
    ax2.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('WiFi趋势预测与总量分析.png', dpi=300, bbox_inches='tight')
    plt.savefig('WiFi趋势预测与总量分析.pdf', bbox_inches='tight')
    plt.close()

if __name__ == "__main__":
    # 读取数据
    data = read_data('data.txt')
    
    # 按WiFi标准合并数据
    merged_data, all_dates = merge_data_by_wifi_standard(data)
    
    # 分析WiFi 5和WiFi 6的关系
    wifi6_surge_start = analyze_wifi5_wifi6_relationship(merged_data)
    
    # 预测WiFi 7的上升时间点
    predicted_wifi7_surge = predict_wifi7_surge(merged_data, wifi6_surge_start)
    
    # 创建总量趋势分析
    total_values = create_total_trend_analysis(merged_data, all_dates)
    
    # 创建综合可视化
    create_comprehensive_visualization(merged_data, all_dates, total_values, predicted_wifi7_surge)
    
    print(f"\n✅ 分析完成！图表已保存为 'WiFi趋势预测与总量分析.png'")
