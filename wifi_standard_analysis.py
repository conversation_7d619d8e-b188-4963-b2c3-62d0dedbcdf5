import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def parse_number(value_str):
    """解析数字，处理万为单位的数据"""
    if value_str == '-' or value_str == '':
        return 0
    
    if '万' in value_str:
        number = float(value_str.replace('万', ''))
        return int(number * 10000)
    
    try:
        return int(value_str)
    except:
        return 0

def parse_date(date_str):
    """将YYYYMM格式转换为datetime对象"""
    year = int(date_str[:4])
    month = int(date_str[4:])
    return datetime(year, month, 1)

def read_data(filename):
    """读取数据文件并解析"""
    data = {}
    current_model = None
    
    with open(filename, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
            
        if '\t' not in line and line != '上线日期(month)\t设备三级分类，产品系列\t上线设备数':
            current_model = line
            data[current_model] = {'dates': [], 'values': []}
            i += 1
            continue
        
        if '上线日期(month)' in line:
            i += 1
            continue
        
        if current_model and '\t' in line:
            parts = line.split('\t')
            if len(parts) >= 3:
                date_str = parts[0].strip()
                value_str = parts[2].strip()
                
                if date_str and len(date_str) == 6:
                    date_obj = parse_date(date_str)
                    value = parse_number(value_str)
                    
                    data[current_model]['dates'].append(date_obj)
                    data[current_model]['values'].append(value)
        
        i += 1
    
    return data

def merge_data_by_wifi_standard(data):
    """按WiFi标准合并数据"""
    # 定义WiFi标准分组
    wifi_groups = {
        'WiFi 5': ['EW1200', 'EW1200G PRO', 'EW1300G'],
        'WiFi 6': ['EW1800GX', 'EW1800GX PRO', 'EW3000GX', 'EW3000GX PRO', 'EW3200GX', 'EW6000GX PRO'],
        'WiFi 7': ['EW5100BE PRO']
    }
    
    # 创建合并后的数据结构
    merged_data = {}
    for wifi_std, models in wifi_groups.items():
        merged_data[wifi_std] = {'dates': [], 'values': [], 'models': models}
    
    # 获取所有日期
    all_dates = set()
    for model, model_data in data.items():
        for date in model_data['dates']:
            all_dates.add(date)
    
    all_dates = sorted(list(all_dates))
    
    # 为每个WiFi标准在每个日期计算总值
    for wifi_std, wifi_data in merged_data.items():
        for date in all_dates:
            total_value = 0
            for model in wifi_data['models']:
                if model in data:
                    model_data = data[model]
                    if date in model_data['dates']:
                        idx = model_data['dates'].index(date)
                        total_value += model_data['values'][idx]
            
            wifi_data['dates'].append(date)
            wifi_data['values'].append(total_value)
    
    return merged_data

def find_first_point_above_threshold(dates, values, threshold=200):
    """找到第一个超过阈值的点"""
    for i, value in enumerate(values):
        if value > threshold:
            return dates[i], value
    return None, None

def create_visualization(merged_data):
    """创建可视化图表"""
    plt.figure(figsize=(16, 10))
    
    colors = {
        'WiFi 5': '#1f77b4',  # 蓝色
        'WiFi 6': '#ff7f0e',  # 橙色
        'WiFi 7': '#2ca02c'   # 绿色
    }
    
    # 绘制主线
    for wifi_std, wifi_data in merged_data.items():
        dates = wifi_data['dates']
        values = wifi_data['values']
        
        plt.plot(dates, values, marker='o', linewidth=3, markersize=5, 
                label=f"{wifi_std} ({', '.join(wifi_data['models'])})", 
                color=colors[wifi_std])
        
        # 找到第一个超过200台的点
        first_date, first_value = find_first_point_above_threshold(dates, values, 200)
        
        if first_date and first_value:
            # 绘制垂直虚线标记
            plt.axvline(x=first_date, color=colors[wifi_std], linestyle='--', alpha=0.7, linewidth=1)
            
            # 在虚线顶部添加标注
            plt.annotate(f'{wifi_std}\n首次>200台\n{first_date.strftime("%Y-%m")}\n{first_value:,}台', 
                        xy=(first_date, first_value), 
                        xytext=(10, 20), 
                        textcoords='offset points',
                        bbox=dict(boxstyle='round,pad=0.3', facecolor=colors[wifi_std], alpha=0.3),
                        fontsize=10,
                        ha='left')
    
    # 设置图表属性
    plt.title('WiFi标准产品上线设备数量走势分析', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('时间', fontsize=12)
    plt.ylabel('产品数量（台）', fontsize=12)
    
    # 设置网格
    plt.grid(True, alpha=0.3)
    
    # 设置图例
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    
    # 自动调整Y轴范围
    plt.ticklabel_format(style='plain', axis='y')
    
    # 旋转x轴标签以避免重叠
    plt.xticks(rotation=45)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('WiFi标准产品走势分析.png', dpi=300, bbox_inches='tight')
    plt.savefig('WiFi标准产品走势分析.pdf', bbox_inches='tight')
    
    # 关闭图表
    plt.close()

def print_summary(merged_data):
    """打印数据摘要"""
    print("WiFi标准数据摘要:")
    print("-" * 50)
    
    for wifi_std, wifi_data in merged_data.items():
        dates = wifi_data['dates']
        values = wifi_data['values']
        
        if not dates:
            continue
            
        max_value = max(values)
        max_date = dates[values.index(max_value)]
        
        current_value = values[-1]
        current_date = dates[-1]
        
        first_date, first_value = find_first_point_above_threshold(dates, values, 200)
        
        print(f"WiFi标准: {wifi_std}")
        print(f"  包含型号: {', '.join(wifi_data['models'])}")
        print(f"  数据点数: {len(dates)}")
        print(f"  最大值: {max_value:,}台 ({max_date.strftime('%Y-%m')})")
        print(f"  当前值: {current_value:,}台 ({current_date.strftime('%Y-%m')})")
        
        if first_date:
            print(f"  首次超过200台: {first_date.strftime('%Y-%m')} ({first_value:,}台)")
        else:
            print(f"  未超过200台")
        
        # 计算最近6个月的趋势
        if len(values) >= 6:
            recent_values = values[-6:]
            trend = "稳定"
            if all(recent_values[i] > recent_values[i-1] for i in range(1, 6)):
                trend = "强劲上升"
            elif all(recent_values[i] < recent_values[i-1] for i in range(1, 6)):
                trend = "持续下降"
            elif sum(recent_values[-3:]) > sum(recent_values[-6:-3]):
                trend = "上升"
            elif sum(recent_values[-3:]) < sum(recent_values[-6:-3]):
                trend = "下降"
            
            print(f"  最近趋势: {trend}")
        
        print()

def analyze_wifi_transition(merged_data):
    """分析WiFi标准转换趋势"""
    print("WiFi标准转换分析:")
    print("-" * 50)
    
    # 获取所有共同的日期
    common_dates = set(merged_data['WiFi 5']['dates'])
    for wifi_std, wifi_data in merged_data.items():
        common_dates = common_dates.intersection(set(wifi_data['dates']))
    
    common_dates = sorted(list(common_dates))
    
    if not common_dates:
        print("没有足够的数据进行WiFi标准转换分析")
        return
    
    # 计算每个日期的市场份额
    market_shares = {date: {} for date in common_dates}
    for date in common_dates:
        total = 0
        for wifi_std, wifi_data in merged_data.items():
            idx = wifi_data['dates'].index(date)
            value = wifi_data['values'][idx]
            total += value
        
        if total > 0:
            for wifi_std, wifi_data in merged_data.items():
                idx = wifi_data['dates'].index(date)
                value = wifi_data['values'][idx]
                market_shares[date][wifi_std] = (value / total) * 100
    
    # 打印关键时间点的市场份额
    key_dates = [common_dates[0], common_dates[len(common_dates)//2], common_dates[-1]]
    
    print("市场份额变化:")
    for date in key_dates:
        print(f"  {date.strftime('%Y-%m')}:")
        for wifi_std in merged_data.keys():
            if wifi_std in market_shares[date]:
                print(f"    {wifi_std}: {market_shares[date][wifi_std]:.1f}%")
    
    print()
    
    # 分析交叉点
    crossover_points = []
    for i in range(1, len(common_dates)):
        prev_date = common_dates[i-1]
        curr_date = common_dates[i]
        
        for std1 in merged_data.keys():
            for std2 in merged_data.keys():
                if std1 != std2:
                    if std1 in market_shares[prev_date] and std2 in market_shares[prev_date] and \
                       std1 in market_shares[curr_date] and std2 in market_shares[curr_date]:
                        
                        if market_shares[prev_date][std1] > market_shares[prev_date][std2] and \
                           market_shares[curr_date][std1] < market_shares[curr_date][std2]:
                            
                            crossover_points.append((curr_date, std1, std2))
    
    if crossover_points:
        print("技术转换点:")
        for date, old_std, new_std in crossover_points:
            print(f"  {date.strftime('%Y-%m')}: {old_std} 被 {new_std} 超越")
    else:
        print("未检测到明显的技术转换点")

if __name__ == "__main__":
    # 读取数据
    data = read_data('data.txt')
    
    # 按WiFi标准合并数据
    merged_data = merge_data_by_wifi_standard(data)
    
    # 打印摘要
    print_summary(merged_data)
    
    # 分析WiFi标准转换
    analyze_wifi_transition(merged_data)
    
    # 创建可视化
    create_visualization(merged_data)
    
    print("可视化图表已生成并保存为 'WiFi标准产品走势分析.png' 和 'WiFi标准产品走势分析.pdf'")
