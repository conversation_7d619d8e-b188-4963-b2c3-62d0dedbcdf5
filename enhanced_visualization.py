import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import seaborn as sns
from scipy.interpolate import make_interp_spline
from scipy.ndimage import gaussian_filter1d
import matplotlib.dates as mdates
import matplotlib
matplotlib.use('Agg')

# 设置高质量绘图参数
plt.style.use('seaborn-v0_8-darkgrid')
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 300
plt.rcParams['savefig.dpi'] = 300
plt.rcParams['font.size'] = 10
plt.rcParams['axes.titlesize'] = 14
plt.rcParams['axes.labelsize'] = 12
plt.rcParams['xtick.labelsize'] = 10
plt.rcParams['ytick.labelsize'] = 10
plt.rcParams['legend.fontsize'] = 10

# 设置颜色主题
colors = {
    'EW1200': '#2E86AB',
    'EW1200G PRO': '#A23B72', 
    'EW1200G-PRO': '#A23B72',
    'EW1300G': '#F18F01',
    'EW1800GX PRO': '#C73E1D',
    'EW1800GX': '#592E83',
    'EW3000GX PRO': '#048A81',
    'EW3000GX': '#54C6EB',
    'EW3200GX': '#F4B942',
    'EW5100BE PRO': '#2ECC71',
    'EW6000GX PRO': '#E74C3C',
    'WiFi 5': '#3498DB',
    'WiFi 6': '#E67E22', 
    'WiFi 7': '#27AE60',
    'Total': '#8E44AD'
}

def read_csv_data(filename):
    """读取CSV文件并处理数据"""
    print("📊 正在读取CSV数据...")
    
    # 读取CSV文件
    df = pd.read_csv(filename)
    print(f"   总记录数: {len(df):,}")
    
    # 处理时间格式
    df['create_time'] = pd.to_datetime(df['create_time'])
    df['year_month'] = df['create_time'].dt.to_period('M')
    
    # 统计每个产品每月的数量
    monthly_counts = df.groupby(['product_class', 'year_month']).size().reset_index(name='count')
    
    # 转换为我们需要的格式
    data = {}
    for product in monthly_counts['product_class'].unique():
        product_data = monthly_counts[monthly_counts['product_class'] == product]
        
        dates = []
        values = []
        
        for _, row in product_data.iterrows():
            date = row['year_month'].to_timestamp()
            dates.append(date)
            values.append(row['count'])
        
        # 标准化产品名称
        clean_name = product.replace('-', ' ')
        data[clean_name] = {'dates': dates, 'values': values}
    
    print(f"   产品型号数: {len(data)}")
    for product, product_data in data.items():
        print(f"   • {product}: {len(product_data['dates'])}个月数据")
    
    return data

def smooth_curve(x, y, smoothing_factor=0.3):
    """使用高斯滤波平滑曲线"""
    if len(y) < 3:
        return x, y
    
    # 应用高斯滤波
    sigma = len(y) * smoothing_factor / 10
    y_smooth = gaussian_filter1d(y, sigma=sigma)
    
    return x, y_smooth

def create_enhanced_individual_visualization(data):
    """创建增强版个人产品走势图"""
    fig, ax = plt.subplots(figsize=(20, 12))
    
    # 设置背景
    fig.patch.set_facecolor('#f8f9fa')
    ax.set_facecolor('#ffffff')
    
    # 绘制每个产品的曲线
    for i, (model, model_data) in enumerate(data.items()):
        if not model_data['dates']:
            continue
            
        dates = model_data['dates']
        values = model_data['values']
        
        # 平滑处理
        x_smooth, y_smooth = smooth_curve(dates, values)
        
        # 获取颜色
        color = colors.get(model, plt.cm.Set3(i % 12))
        
        # 绘制平滑曲线
        ax.plot(x_smooth, y_smooth, linewidth=3, alpha=0.8, 
                label=model, color=color, marker='o', markersize=4, 
                markerfacecolor='white', markeredgecolor=color, markeredgewidth=2)
        
        # 找到并标记峰值
        if values:
            max_value = max(values)
            max_date = dates[values.index(max_value)]
            
            # 标记峰值点
            ax.scatter(max_date, max_value, s=100, color=color, 
                      edgecolor='white', linewidth=2, zorder=5)
            
            # 添加峰值标注（只对前几个主要产品）
            if max_value > 5000:
                ax.annotate(f'{max_value:,}', 
                           xy=(max_date, max_value), 
                           xytext=(10, 15), 
                           textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.3', 
                                   facecolor=color, alpha=0.7, edgecolor='white'),
                           fontsize=9, fontweight='bold', color='white',
                           ha='center')
    
    # 设置标题和标签
    ax.set_title('🚀 产品上线设备数量走势分析', fontsize=20, fontweight='bold', 
                pad=30, color='#2c3e50')
    ax.set_xlabel('时间', fontsize=14, fontweight='bold', color='#34495e')
    ax.set_ylabel('产品数量（台）', fontsize=14, fontweight='bold', color='#34495e')
    
    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # 设置图例
    legend = ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', 
                      frameon=True, fancybox=True, shadow=True,
                      fontsize=11, title='产品型号', title_fontsize=12)
    legend.get_frame().set_facecolor('#f8f9fa')
    legend.get_frame().set_alpha(0.9)
    
    # 格式化坐标轴
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 设置Y轴格式
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
    
    # 添加边框
    for spine in ax.spines.values():
        spine.set_edgecolor('#bdc3c7')
        spine.set_linewidth(1)
    
    plt.tight_layout()
    plt.savefig('产品走势分析.png', dpi=300, bbox_inches='tight', 
                facecolor='#f8f9fa', edgecolor='none')
    plt.close()

def merge_data_by_wifi_standard(data):
    """按WiFi标准合并数据"""
    wifi_groups = {
        'WiFi 5': ['EW1200', 'EW1200G PRO', 'EW1300G'],
        'WiFi 6': ['EW1800GX', 'EW1800GX PRO', 'EW3000GX', 'EW3000GX PRO', 'EW3200GX', 'EW6000GX PRO'],
        'WiFi 7': ['EW5100BE PRO']
    }
    
    # 获取所有日期
    all_dates = set()
    for model, model_data in data.items():
        for date in model_data['dates']:
            all_dates.add(date)
    
    all_dates = sorted(list(all_dates))
    
    merged_data = {}
    for wifi_std, models in wifi_groups.items():
        merged_data[wifi_std] = {'dates': [], 'values': [], 'models': models}
        
        for date in all_dates:
            total_value = 0
            for model in models:
                if model in data:
                    model_data = data[model]
                    if date in model_data['dates']:
                        idx = model_data['dates'].index(date)
                        total_value += model_data['values'][idx]
            
            merged_data[wifi_std]['dates'].append(date)
            merged_data[wifi_std]['values'].append(total_value)
    
    return merged_data, all_dates

def create_enhanced_wifi_standard_visualization(merged_data):
    """创建增强版WiFi标准走势图"""
    fig, ax = plt.subplots(figsize=(20, 12))
    
    # 设置背景
    fig.patch.set_facecolor('#f8f9fa')
    ax.set_facecolor('#ffffff')
    
    # 绘制WiFi标准曲线
    for wifi_std, wifi_data in merged_data.items():
        dates = wifi_data['dates']
        values = wifi_data['values']
        
        if not dates:
            continue
        
        # 平滑处理
        x_smooth, y_smooth = smooth_curve(dates, values)
        
        color = colors[wifi_std]
        
        # 绘制主曲线
        ax.plot(x_smooth, y_smooth, linewidth=4, alpha=0.9, 
                label=f"{wifi_std} ({', '.join(wifi_data['models'])})", 
                color=color, marker='o', markersize=6,
                markerfacecolor='white', markeredgecolor=color, markeredgewidth=2)
        
        # 填充区域
        ax.fill_between(x_smooth, y_smooth, alpha=0.2, color=color)
        
        # 标记峰值
        if values:
            max_value = max(values)
            max_date = dates[values.index(max_value)]
            
            ax.scatter(max_date, max_value, s=150, color=color, 
                      edgecolor='white', linewidth=3, zorder=5)
            
            # 峰值标注
            ax.annotate(f'{wifi_std}\n峰值: {max_value:,}台\n{max_date.strftime("%Y-%m")}', 
                       xy=(max_date, max_value), 
                       xytext=(20, 30), 
                       textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.5', 
                               facecolor=color, alpha=0.8, edgecolor='white'),
                       fontsize=10, fontweight='bold', color='white',
                       ha='center',
                       arrowprops=dict(arrowstyle='->', color=color, lw=2))
    
    # 设置标题和标签
    ax.set_title('📡 WiFi标准产品上线设备数量走势分析', fontsize=20, fontweight='bold', 
                pad=30, color='#2c3e50')
    ax.set_xlabel('时间', fontsize=14, fontweight='bold', color='#34495e')
    ax.set_ylabel('产品数量（台）', fontsize=14, fontweight='bold', color='#34495e')
    
    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # 设置图例
    legend = ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', 
                      frameon=True, fancybox=True, shadow=True,
                      fontsize=12, title='WiFi标准', title_fontsize=14)
    legend.get_frame().set_facecolor('#f8f9fa')
    legend.get_frame().set_alpha(0.9)
    
    # 格式化坐标轴
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
    
    # 添加边框
    for spine in ax.spines.values():
        spine.set_edgecolor('#bdc3c7')
        spine.set_linewidth(1)
    
    plt.tight_layout()
    plt.savefig('WiFi标准产品走势分析.png', dpi=300, bbox_inches='tight', 
                facecolor='#f8f9fa', edgecolor='none')
    plt.close()

def create_enhanced_comprehensive_visualization(merged_data, all_dates):
    """创建增强版综合分析图"""
    # 计算总量
    total_values = []
    for date in all_dates:
        total = 0
        for wifi_std, wifi_data in merged_data.items():
            if date in wifi_data['dates']:
                idx = wifi_data['dates'].index(date)
                total += wifi_data['values'][idx]
        total_values.append(total)
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(20, 16))
    
    # 设置背景
    fig.patch.set_facecolor('#f8f9fa')
    ax1.set_facecolor('#ffffff')
    ax2.set_facecolor('#ffffff')
    
    # 上图：WiFi标准对比
    for wifi_std, wifi_data in merged_data.items():
        dates = wifi_data['dates']
        values = wifi_data['values']
        
        if not dates:
            continue
        
        # 平滑处理
        x_smooth, y_smooth = smooth_curve(dates, values)
        
        color = colors[wifi_std]
        
        ax1.plot(x_smooth, y_smooth, linewidth=3, alpha=0.9, 
                label=wifi_std, color=color, marker='o', markersize=5,
                markerfacecolor='white', markeredgecolor=color, markeredgewidth=2)
        
        ax1.fill_between(x_smooth, y_smooth, alpha=0.15, color=color)
    
    ax1.set_title('📊 WiFi标准产品走势对比分析', fontsize=18, fontweight='bold', 
                 pad=20, color='#2c3e50')
    ax1.set_ylabel('产品数量（台）', fontsize=12, fontweight='bold', color='#34495e')
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    
    # 下图：总量趋势
    x_smooth, y_smooth = smooth_curve(all_dates, total_values)
    
    ax2.plot(x_smooth, y_smooth, linewidth=4, color=colors['Total'], 
             marker='o', markersize=6, markerfacecolor='white', 
             markeredgecolor=colors['Total'], markeredgewidth=2,
             label='总量', alpha=0.9)
    
    ax2.fill_between(x_smooth, y_smooth, alpha=0.2, color=colors['Total'])
    
    # 添加趋势线
    if len(all_dates) > 1:
        x_numeric = [(date - all_dates[0]).days for date in all_dates]
        z = np.polyfit(x_numeric, total_values, 2)
        p = np.poly1d(z)
        ax2.plot(all_dates, p(x_numeric), "--", alpha=0.7, color='#95a5a6', 
                linewidth=2, label='趋势线')
    
    # 标记峰值
    max_total = max(total_values)
    max_date = all_dates[total_values.index(max_total)]
    ax2.scatter(max_date, max_total, s=200, color=colors['Total'], 
               edgecolor='white', linewidth=3, zorder=5)
    
    ax2.annotate(f'历史峰值\n{max_total:,}台\n{max_date.strftime("%Y-%m")}', 
                xy=(max_date, max_total), 
                xytext=(30, 40), 
                textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.5', 
                         facecolor=colors['Total'], alpha=0.8, edgecolor='white'),
                fontsize=11, fontweight='bold', color='white',
                ha='center',
                arrowprops=dict(arrowstyle='->', color=colors['Total'], lw=2))
    
    ax2.set_title('📈 总体市场规模趋势', fontsize=18, fontweight='bold', 
                 pad=20, color='#2c3e50')
    ax2.set_xlabel('时间', fontsize=12, fontweight='bold', color='#34495e')
    ax2.set_ylabel('总产品数量（台）', fontsize=12, fontweight='bold', color='#34495e')
    ax2.grid(True, alpha=0.3)
    ax2.legend(fontsize=11, frameon=True, fancybox=True, shadow=True)
    
    # 格式化坐标轴
    for ax in [ax1, ax2]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
        ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x:,.0f}'))
        
        # 添加边框
        for spine in ax.spines.values():
            spine.set_edgecolor('#bdc3c7')
            spine.set_linewidth(1)
    
    plt.tight_layout()
    plt.savefig('WiFi趋势预测与总量分析.png', dpi=300, bbox_inches='tight', 
                facecolor='#f8f9fa', edgecolor='none')
    plt.close()

if __name__ == "__main__":
    print("🚀 开始生成增强版可视化图表...")
    
    # 读取CSV数据
    data = read_csv_data('SN.csv')
    
    print("\n📊 生成产品走势分析图...")
    create_enhanced_individual_visualization(data)
    
    print("📡 生成WiFi标准产品走势分析图...")
    merged_data, all_dates = merge_data_by_wifi_standard(data)
    create_enhanced_wifi_standard_visualization(merged_data)
    
    print("📈 生成WiFi趋势预测与总量分析图...")
    create_enhanced_comprehensive_visualization(merged_data, all_dates)
    
    print("\n✅ 所有图表生成完成！")
    print("   • 产品走势分析.png")
    print("   • WiFi标准产品走势分析.png") 
    print("   • WiFi趋势预测与总量分析.png")
