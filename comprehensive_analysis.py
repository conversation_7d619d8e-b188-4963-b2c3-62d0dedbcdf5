import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime
import seaborn as sns
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import matplotlib
matplotlib.use('Agg')

# 设置高质量绘图参数
plt.style.use('seaborn-v0_8-darkgrid')
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.facecolor'] = 'white'
plt.rcParams['axes.facecolor'] = '#f8f9fa'
plt.rcParams['grid.alpha'] = 0.3
plt.rcParams['axes.edgecolor'] = '#dee2e6'
plt.rcParams['axes.linewidth'] = 0.8

def load_and_process_data(filename):
    """加载并处理CSV数据"""
    print("📊 正在加载数据...")
    
    # 读取CSV文件
    df = pd.read_csv(filename)
    print(f"   总记录数: {len(df):,}")
    
    # 转换时间格式
    df['create_time'] = pd.to_datetime(df['create_time'])
    df['year_month'] = df['create_time'].dt.to_period('M')
    
    # 统计每个产品每月的数量
    monthly_counts = df.groupby(['product_class', 'year_month']).size().reset_index(name='count')
    
    # 转换为透视表
    pivot_data = monthly_counts.pivot(index='year_month', columns='product_class', values='count').fillna(0)
    
    print(f"   产品型号数: {len(pivot_data.columns)}")
    print(f"   时间跨度: {pivot_data.index.min()} - {pivot_data.index.max()}")
    
    return pivot_data

def create_wifi_groups(pivot_data):
    """创建WiFi标准分组"""
    wifi_groups = {
        'WiFi 5': [],
        'WiFi 6': [],
        'WiFi 7': []
    }
    
    # 根据产品名称分组
    for product in pivot_data.columns:
        product_clean = product.replace('-', '').replace(' ', '').upper()
        
        if any(x in product_clean for x in ['EW1200', 'EW1300G']):
            wifi_groups['WiFi 5'].append(product)
        elif any(x in product_clean for x in ['EW1800', 'EW3000', 'EW3200', 'EW6000']):
            wifi_groups['WiFi 6'].append(product)
        elif 'EW5100' in product_clean or 'BE' in product_clean:
            wifi_groups['WiFi 7'].append(product)
    
    print("\n📡 WiFi标准分组:")
    for wifi_std, products in wifi_groups.items():
        print(f"   {wifi_std}: {products}")
    
    return wifi_groups

def create_premium_chart_1(pivot_data):
    """创建高端产品走势分析图"""
    fig, ax = plt.subplots(figsize=(20, 12))
    
    # 设置配色方案
    colors = plt.cm.Set3(np.linspace(0, 1, len(pivot_data.columns)))
    
    # 转换时间轴
    dates = [period.to_timestamp() for period in pivot_data.index]
    
    # 绘制每个产品的曲线
    for i, product in enumerate(pivot_data.columns):
        values = pivot_data[product].values
        
        # 只显示有数据的产品
        if values.sum() > 100:  # 过滤掉数据量太小的产品
            ax.plot(dates, values, marker='o', linewidth=2.5, markersize=4, 
                   label=product, color=colors[i], alpha=0.8)
            
            # 标记峰值点
            max_idx = np.argmax(values)
            max_value = values[max_idx]
            if max_value > 200:
                ax.annotate(f'{max_value:,}', 
                           xy=(dates[max_idx], max_value),
                           xytext=(10, 10), textcoords='offset points',
                           bbox=dict(boxstyle='round,pad=0.3', facecolor=colors[i], alpha=0.7),
                           fontsize=9, ha='left')
    
    # 设置标题和标签
    ax.set_title('🚀 产品上线设备数量走势分析', fontsize=20, fontweight='bold', pad=30)
    ax.set_xlabel('时间', fontsize=14, fontweight='bold')
    ax.set_ylabel('设备数量（台）', fontsize=14, fontweight='bold')
    
    # 格式化x轴
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 设置网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_facecolor('#f8f9fa')
    
    # 设置图例
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10, 
             frameon=True, fancybox=True, shadow=True)
    
    # 添加背景渐变效果
    ax.set_axisbelow(True)
    
    plt.tight_layout()
    plt.savefig('产品走势分析.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.savefig('产品走势分析.pdf', bbox_inches='tight', facecolor='white')
    plt.close()

def create_premium_chart_2(pivot_data, wifi_groups):
    """创建高端WiFi标准产品走势分析图"""
    fig, ax = plt.subplots(figsize=(20, 12))
    
    # 合并WiFi标准数据
    dates = [period.to_timestamp() for period in pivot_data.index]
    wifi_data = {}
    
    colors = {
        'WiFi 5': '#2E86AB',  # 深蓝色
        'WiFi 6': '#A23B72',  # 深紫红色  
        'WiFi 7': '#F18F01'   # 橙色
    }
    
    for wifi_std, products in wifi_groups.items():
        if products:
            wifi_data[wifi_std] = pivot_data[products].sum(axis=1).values
        else:
            wifi_data[wifi_std] = np.zeros(len(pivot_data))
    
    # 计算总量用于市场份额
    total_values = sum(wifi_data.values())
    
    # 绘制WiFi标准曲线
    for wifi_std, values in wifi_data.items():
        if values.sum() > 0:
            ax.plot(dates, values, marker='o', linewidth=4, markersize=6, 
                   label=wifi_std, color=colors[wifi_std], alpha=0.9)
            
            # 计算当前市场份额
            current_share = (values[-1] / total_values[-1] * 100) if total_values[-1] > 0 else 0
            
            # 在右侧添加市场份额标注
            ax.text(dates[-1], values[-1], f'{current_share:.1f}%', 
                   fontsize=12, fontweight='bold', color=colors[wifi_std],
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8),
                   ha='left', va='center')
    
    # 设置标题和标签
    ax.set_title('📡 WiFi标准产品上线设备数量走势分析', fontsize=20, fontweight='bold', pad=30)
    ax.set_xlabel('时间', fontsize=14, fontweight='bold')
    ax.set_ylabel('设备数量（台）', fontsize=14, fontweight='bold')
    
    # 格式化x轴
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    # 设置网格和背景
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_facecolor('#f8f9fa')
    
    # 设置图例
    ax.legend(loc='upper left', fontsize=14, frameon=True, fancybox=True, shadow=True)
    
    # 添加市场份额说明
    ax.text(0.02, 0.98, '右侧数字为当前市场份额占比', transform=ax.transAxes,
           fontsize=11, verticalalignment='top', 
           bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.7))
    
    plt.tight_layout()
    plt.savefig('WiFi标准产品走势分析.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.savefig('WiFi标准产品走势分析.pdf', bbox_inches='tight', facecolor='white')
    plt.close()
    
    return wifi_data

def create_premium_chart_3(pivot_data, wifi_data):
    """创建高端WiFi趋势预测与总量分析图"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(20, 16))
    
    dates = [period.to_timestamp() for period in pivot_data.index]
    
    colors = {
        'WiFi 5': '#2E86AB',
        'WiFi 6': '#A23B72', 
        'WiFi 7': '#F18F01',
        'Total': '#E63946'
    }
    
    # 上图：WiFi标准对比
    total_values = sum(wifi_data.values())
    
    for wifi_std, values in wifi_data.items():
        if values.sum() > 0:
            ax1.plot(dates, values, marker='o', linewidth=3, markersize=5, 
                    label=wifi_std, color=colors[wifi_std], alpha=0.9)
            
            # 计算并显示市场份额
            current_share = (values[-1] / total_values[-1] * 100) if total_values[-1] > 0 else 0
            ax1.text(dates[-1], values[-1], f'{current_share:.1f}%', 
                    fontsize=11, fontweight='bold', color=colors[wifi_std],
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8),
                    ha='left', va='center')
    
    # 预测WiFi 7上升趋势线
    wifi7_values = wifi_data['WiFi 7']
    if wifi7_values.sum() > 0:
        # 找到WiFi 7开始增长的点
        start_idx = next(i for i, v in enumerate(wifi7_values) if v > 0)
        if start_idx < len(wifi7_values) - 3:
            # 简单的指数增长预测
            recent_growth = wifi7_values[-3:]
            if len(recent_growth) >= 2 and recent_growth[-1] > recent_growth[-2]:
                growth_rate = (recent_growth[-1] / recent_growth[-2]) if recent_growth[-2] > 0 else 1.5
                
                # 预测未来6个月
                future_dates = pd.date_range(dates[-1], periods=7, freq='M')[1:]
                future_values = [wifi7_values[-1] * (growth_rate ** i) for i in range(1, 7)]
                
                ax1.plot(future_dates, future_values, '--', color=colors['WiFi 7'], 
                        alpha=0.6, linewidth=2, label='WiFi 7 预测趋势')
    
    ax1.set_title('📊 WiFi标准产品走势对比分析', fontsize=18, fontweight='bold', pad=20)
    ax1.set_ylabel('设备数量（台）', fontsize=12, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    ax1.legend(fontsize=12, frameon=True, fancybox=True, shadow=True)
    ax1.set_facecolor('#f8f9fa')
    
    # 下图：总量趋势
    ax2.plot(dates, total_values, marker='o', linewidth=4, markersize=6, 
             color=colors['Total'], label='总量', alpha=0.9)
    
    # 添加趋势线
    x_numeric = np.arange(len(dates))
    z = np.polyfit(x_numeric, total_values, 2)
    p = np.poly1d(z)
    ax2.plot(dates, p(x_numeric), "--", alpha=0.7, color='gray', linewidth=2, label='趋势线')
    
    # 标记峰值
    max_idx = np.argmax(total_values)
    max_value = total_values[max_idx]
    ax2.annotate(f'历史峰值\n{max_value:,}台\n{dates[max_idx].strftime("%Y-%m")}', 
                xy=(dates[max_idx], max_value), 
                xytext=(20, 30), textcoords='offset points',
                bbox=dict(boxstyle='round,pad=0.5', facecolor=colors['Total'], alpha=0.7),
                fontsize=11, ha='left', fontweight='bold',
                arrowprops=dict(arrowstyle='->', color=colors['Total'], lw=2))
    
    # 当前值标注
    current_value = total_values[-1]
    ax2.text(dates[-1], current_value, f'{current_value:,}台', 
            fontsize=12, fontweight='bold', color=colors['Total'],
            bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8),
            ha='left', va='center')
    
    ax2.set_title('📈 总体市场规模趋势分析', fontsize=18, fontweight='bold', pad=20)
    ax2.set_xlabel('时间', fontsize=12, fontweight='bold')
    ax2.set_ylabel('总设备数量（台）', fontsize=12, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    ax2.legend(fontsize=12, frameon=True, fancybox=True, shadow=True)
    ax2.set_facecolor('#f8f9fa')
    
    # 格式化x轴
    for ax in [ax1, ax2]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45, ha='right')
    
    plt.tight_layout()
    plt.savefig('WiFi趋势预测与总量分析.png', dpi=300, bbox_inches='tight', facecolor='white')
    plt.savefig('WiFi趋势预测与总量分析.pdf', bbox_inches='tight', facecolor='white')
    plt.close()

def print_analysis_summary(pivot_data, wifi_data):
    """打印分析摘要"""
    print("\n" + "="*80)
    print("📊 数据分析摘要")
    print("="*80)
    
    # 总体统计
    total_devices = pivot_data.sum().sum()
    print(f"📈 总设备数量: {total_devices:,}台")
    print(f"📅 数据时间跨度: {pivot_data.index.min()} - {pivot_data.index.max()}")
    print(f"🏷️  产品型号数量: {len(pivot_data.columns)}")
    
    # WiFi标准统计
    print(f"\n📡 WiFi标准分布:")
    total_current = sum(values[-1] for values in wifi_data.values())
    
    for wifi_std, values in wifi_data.items():
        current_value = values[-1]
        max_value = max(values)
        share = (current_value / total_current * 100) if total_current > 0 else 0
        
        print(f"   {wifi_std}: 当前{current_value:,}台 (份额{share:.1f}%), 峰值{max_value:,}台")
    
    # 产品排行
    print(f"\n🏆 产品排行榜 (按当前月份):")
    current_month_data = pivot_data.iloc[-1].sort_values(ascending=False)
    for i, (product, count) in enumerate(current_month_data.head(10).items(), 1):
        if count > 0:
            print(f"   {i:2d}. {product}: {count:,}台")

if __name__ == "__main__":
    # 加载数据
    pivot_data = load_and_process_data('SN.csv')
    
    # 创建WiFi分组
    wifi_groups = create_wifi_groups(pivot_data)
    
    # 生成三个高端图表
    print("\n🎨 正在生成图表...")
    
    print("   1/3 产品走势分析图...")
    create_premium_chart_1(pivot_data)
    
    print("   2/3 WiFi标准产品走势分析图...")
    wifi_data = create_premium_chart_2(pivot_data, wifi_groups)
    
    print("   3/3 WiFi趋势预测与总量分析图...")
    create_premium_chart_3(pivot_data, wifi_data)
    
    # 打印分析摘要
    print_analysis_summary(pivot_data, wifi_data)
    
    print(f"\n✅ 分析完成！已生成以下图表:")
    print(f"   📊 产品走势分析.png")
    print(f"   📡 WiFi标准产品走势分析.png") 
    print(f"   📈 WiFi趋势预测与总量分析.png")
